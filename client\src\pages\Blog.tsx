import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useEffect } from 'react';

const Blog = () => {
  const { t, i18n } = useTranslation();
  const currentPath = window.location.pathname.split('/').filter(Boolean);
  const languages = [
    { code: 'en' }, { code: 'zh' }, { code: 'es' }, { code: 'de' }, { code: 'fr' }, { code: 'ja' }, { code: 'pt' }, { code: 'ru' }, { code: 'ar' }, { code: 'hi' }
  ];
  const hasLangCode = languages.some(lang => lang.code === currentPath[0]);

  useEffect(() => {
    const lang = window.location.pathname.split('/')[1];
    if (lang && languages.some(l => l.code === lang) && i18n.language !== lang) {
      i18n.changeLanguage(lang);
    } else if (!lang && i18n.language !== 'en') {
      i18n.changeLanguage('en');
    }
  }, [window.location.pathname, i18n]);

  return (
    <>
      <Helmet>
        <title>{t('blog.title')} | {t('footer.name')}</title>
        <meta name="description" content={t('blog.intro')} />
        <meta name="keywords" content="password, blog, security, tips, 密码, 博客, 安全, 建议" />
        <link rel="canonical" href={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog`} />
        <meta property="og:title" content={`${t('blog.title')} | ${t('footer.name')}`} />
        <meta property="og:description" content={t('blog.intro')} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/blog`} />
        <meta property="og:image" content="https://password-generator.me/og-image.jpg" />
      </Helmet>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="container mx-auto px-4 py-10 max-w-3xl flex-grow">
          <h1 className="text-3xl font-bold mb-6">{t('blog.title')}</h1>
          <p className="mb-8 text-lg text-dark-light">{t('blog.intro')}</p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <a href={hasLangCode && currentPath[0] !== 'en' ? `/${i18n.language}/blog/secure-password` : '/blog/secure-password'} className="block p-6 bg-white rounded-lg shadow hover:shadow-lg transition">
              <h2 className="text-xl font-semibold mb-2">{t('blog.securePassword.title')}</h2>
              <p className="text-gray-600">{t('blog.securePassword.content').slice(0, 40)}...</p>
            </a>
            <a href={hasLangCode && currentPath[0] !== 'en' ? `/${i18n.language}/blog/weak-password-risks` : '/blog/weak-password-risks'} className="block p-6 bg-white rounded-lg shadow hover:shadow-lg transition">
              <h2 className="text-xl font-semibold mb-2">{t('blog.weakPasswordRisks.title')}</h2>
              <p className="text-gray-600">{t('blog.weakPasswordRisks.content').slice(0, 40)}...</p>
            </a>
            <a href={hasLangCode && currentPath[0] !== 'en' ? `/${i18n.language}/blog/management-tips` : '/blog/management-tips'} className="block p-6 bg-white rounded-lg shadow hover:shadow-lg transition">
              <h2 className="text-xl font-semibold mb-2">{t('blog.managementTips.title')}</h2>
              <p className="text-gray-600">{t('blog.managementTips.content').slice(0, 40)}...</p>
            </a>
            <a href={hasLangCode && currentPath[0] !== 'en' ? `/${i18n.language}/blog/common-mistakes` : '/blog/common-mistakes'} className="block p-6 bg-white rounded-lg shadow hover:shadow-lg transition">
              <h2 className="text-xl font-semibold mb-2">{t('blog.commonMistakes.title')}</h2>
              <p className="text-gray-600">{t('blog.commonMistakes.content').slice(0, 40)}...</p>
            </a>
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default Blog;